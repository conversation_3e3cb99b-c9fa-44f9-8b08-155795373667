#!/usr/bin/env python3
"""
身分證件資料處理 API 伺服器 v2.0
提供智能身分證 OCR 文字辨識和表單提交功能
支援正面/背面自動檢測和對應欄位回傳
"""

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import tempfile
import uuid
from datetime import datetime
import logging
from typing import Dict, Any, Optional
from docx import Document

from id_analyzer import IDCardAnalyzer
from form_submitter import FormSubmitter
from assistants_manager import AssistantsManager
from passbook_analyzer import PassbookAnalyzer

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/api_server.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 建立 FastAPI 應用
app = FastAPI(
    title="智能身分證件資料處理 API",
    description="""
    🔍 智能身分證 OCR 文字辨識 API

    功能特色：
    - 🤖 自動檢測身分證正面/背面
    - 📊 支援多種 AI 模型 (Gemini, OpenAI, Claude)
    - 🎯 根據面別回傳對應欄位
    - 📝 一站式分析+表單提交
    - 🔄 多模型比較分析

    支援格式：PNG, JPG, JPEG, WEBP
    檔案大小：最大 16MB
    """,
    version="2.0.0",
    root_path="/api/v1/langlive"
)

# 設定 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生產環境中應該限制特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 設定檔案上傳大小限制 (5MB)
MAX_FILE_SIZE = 5 * 1024 * 1024

# 使用次數限制
MAX_USAGE_COUNT = 30
USAGE_COUNT_FILE = 'usage_count.txt'

# 允許的檔案類型
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp'}
ALLOWED_TEXT_EXTENSIONS = {'md', 'txt', 'doc', 'docx'}

# Pydantic 模型
class IDCardFrontData(BaseModel):
    """身分證正面資料模型"""
    name: Optional[str] = None
    id_number: Optional[str] = None
    birth_date: Optional[str] = None
    issue_date: Optional[str] = None
    gender: Optional[str] = None

class IDCardBackData(BaseModel):
    """身分證背面資料模型"""
    father: Optional[str] = None
    mother: Optional[str] = None
    spouse: Optional[str] = None
    military_service: Optional[str] = None
    birth_place: Optional[str] = None
    address: Optional[str] = None

class PassbookData(BaseModel):
    """存摺資料模型"""
    account_name: Optional[str] = None
    account_number: Optional[str] = None
    confidence: Optional[float] = None

class AnalysisDetails(BaseModel):
    """分析詳細資訊"""
    name_confidence: Optional[float] = None
    id_confidence: Optional[float] = None
    birth_date_confidence: Optional[float] = None
    issue_date_confidence: Optional[float] = None
    gender_confidence: Optional[float] = None
    father_confidence: Optional[float] = None
    mother_confidence: Optional[float] = None
    spouse_confidence: Optional[float] = None
    military_service_confidence: Optional[float] = None
    birth_place_confidence: Optional[float] = None
    address_confidence: Optional[float] = None

class FormData(BaseModel):
    """表單提交資料模型"""
    name: Optional[str] = None
    id_number: Optional[str] = None
    birth_date: Optional[str] = None
    issue_date: Optional[str] = None
    gender: Optional[str] = None
    father: Optional[str] = None
    mother: Optional[str] = None
    spouse: Optional[str] = None
    military_service: Optional[str] = None
    birth_place: Optional[str] = None
    address: Optional[str] = None
    confidence: Optional[float] = None

class AnalysisResponse(BaseModel):
    """分析回應模型"""
    success: bool
    request_id: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    code: Optional[str] = None

class XiabianChatRequest(BaseModel):
    """小編對話請求模型"""
    message: str
    thread_id: Optional[str] = None

class XiabianChatResponse(BaseModel):
    """小編對話回應模型"""
    success: bool
    request_id: str
    xiabian_name: str
    response: Optional[str] = None
    thread_id: Optional[str] = None
    run_id: Optional[str] = None
    error: Optional[str] = None

# 全域變數
analyzer = None
passbook_analyzer = None
submitter = None
assistants_manager = None

def allowed_file(filename: str) -> bool:
    """檢查檔案類型是否允許"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def allowed_text_file(filename: str) -> bool:
    """檢查文字檔案類型是否允許"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_TEXT_EXTENSIONS

def get_usage_count() -> int:
    """取得目前使用次數"""
    try:
        if os.path.exists(USAGE_COUNT_FILE):
            with open(USAGE_COUNT_FILE, 'r') as f:
                return int(f.read().strip())
        return 0
    except:
        return 0

def increment_usage_count() -> int:
    """增加使用次數並回傳新的次數"""
    current_count = get_usage_count()
    new_count = current_count + 1
    try:
        with open(USAGE_COUNT_FILE, 'w') as f:
            f.write(str(new_count))
        return new_count
    except:
        return current_count

def reset_usage_count() -> bool:
    """重置使用次數"""
    try:
        with open(USAGE_COUNT_FILE, 'w') as f:
            f.write('0')
        return True
    except:
        return False

def check_usage_limit() -> bool:
    """檢查是否超過使用限制"""
    return get_usage_count() < MAX_USAGE_COUNT

def extract_text_from_docx(file_path: str) -> str:
    """從 DOCX 檔案提取文字內容"""
    try:
        doc = Document(file_path)
        text_content = []

        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text.strip())

        return '\n'.join(text_content)
    except Exception as e:
        raise Exception(f"無法讀取 DOCX 檔案: {str(e)}")

def extract_text_from_doc(file_path: str) -> str:
    """從 DOC 檔案提取文字內容 (需要額外處理)"""
    # DOC 格式比較複雜，這裡先返回錯誤訊息
    # 如果需要支援 DOC，可以使用 python-docx2txt 或其他套件
    raise Exception("目前暫不支援 .doc 格式，請使用 .docx 格式")

def init_services() -> bool:
    """初始化服務"""
    global analyzer, passbook_analyzer, submitter, assistants_manager

    try:
        # 載入環境變數
        from dotenv import load_dotenv
        load_dotenv()

        # 初始化分析器
        analyzer = IDCardAnalyzer()
        logger.info("身分證分析器初始化成功")

        # 初始化存摺分析器
        passbook_analyzer = PassbookAnalyzer()
        logger.info("存摺分析器初始化成功")

        # 初始化表單提交器
        submitter = FormSubmitter()
        logger.info("表單提交器初始化成功")

        # 初始化 Assistants 管理器
        assistants_manager = AssistantsManager()
        logger.info("Assistants 管理器初始化成功")

        return True

    except Exception as e:
        logger.error(f"服務初始化失敗: {e}")
        return False

def format_analysis_response(result: Dict[str, Any], request_id: str) -> Dict[str, Any]:
    """格式化分析回應，根據檢測到的面別回傳對應欄位"""
    detected_side = result.get('detected_side', 'unknown')

    base_data = {
        'confidence': result.get('confidence', 0),
        'details': result.get('details', {}),
        'timestamp': result.get('timestamp'),
        'detected_side': detected_side,
        'source': result.get('source', 'unknown')
    }

    if detected_side == 'front':
        # 正面欄位
        data = {
            **base_data,
            'name': result.get('name'),
            'id_number': result.get('id_number'),
            'birth_date': result.get('birth_date'),
            'issue_date': result.get('issue_date'),
            'gender': result.get('gender')
        }
    elif detected_side == 'back':
        # 背面欄位
        data = {
            **base_data,
            'father': result.get('father'),
            'mother': result.get('mother'),
            'spouse': result.get('spouse'),
            'military_service': result.get('military_service'),
            'birth_place': result.get('birth_place'),
            'address': result.get('address')
        }
    else:
        # 未知面別，回傳所有可能的欄位
        data = {
            **base_data,
            'name': result.get('name'),
            'id_number': result.get('id_number'),
            'birth_date': result.get('birth_date'),
            'issue_date': result.get('issue_date'),
            'gender': result.get('gender'),
            'father': result.get('father'),
            'mother': result.get('mother'),
            'spouse': result.get('spouse'),
            'military_service': result.get('military_service'),
            'birth_place': result.get('birth_place'),
            'address': result.get('address')
        }

    return {
        'success': True,
        'request_id': request_id,
        'data': data
    }

@app.get("/")
async def root():
    """API 根端點"""
    return {
        "message": "🔍 智能身分證件資料處理 API v2.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0',
        'services': {
            'analyzer': analyzer is not None,
            'passbook_analyzer': passbook_analyzer is not None,
            'submitter': submitter is not None,
            'assistants_manager': assistants_manager is not None
        },
        'available_apis': {
            'gemini': analyzer.gemini_model is not None if analyzer else False,
            'openai': analyzer.openai_api_key is not None if analyzer else False,
            'claude': analyzer.bedrock_client is not None if analyzer else False
        } if analyzer else {}
    }

@app.post("/api/v1/analyze")
async def analyze_id_card(
    image: UploadFile = File(...),
    gemini_api_key: str = Form(None, description="Gemini API Key (如果不提供則使用系統預設)")
):
    """
    身分證分析 - 智能檢測正反面

    自動檢測身分證正面或背面，並提取對應欄位：
    - 正面：姓名、身分證號碼、出生日期、發證日期、性別
    - 背面：父親、母親、配偶、役別、出生地、住址

    Args:
        image: 身分證圖片檔案 (支援 PNG, JPG, JPEG, WEBP)
        gemini_api_key: Gemini API Key (可選，如果不提供則使用系統預設)
    """
    try:
        # 檢查使用次數限制
        if not check_usage_limit():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    'success': False,
                    'error': f'已達到使用上限 ({MAX_USAGE_COUNT} 次)，請聯繫管理員重置',
                    'code': 'USAGE_LIMIT_EXCEEDED',
                    'current_usage': get_usage_count(),
                    'max_usage': MAX_USAGE_COUNT
                }
            )

        # 檢查服務是否已初始化
        if analyzer is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    'success': False,
                    'error': 'ID card analyzer service is not available',
                    'code': 'SERVICE_UNAVAILABLE'
                }
            )

        # 檢查檔案是否存在
        if not image.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    'success': False,
                    'error': 'No file uploaded',
                    'code': 'NO_FILE'
                }
            )

        # 檢查檔案類型
        if not allowed_file(image.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    'success': False,
                    'error': f'File type not allowed. Supported types: {", ".join(ALLOWED_EXTENSIONS)}',
                    'code': 'INVALID_FILE_TYPE'
                }
            )

        # 讀取檔案內容並檢查大小
        content = await image.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail={
                    'success': False,
                    'error': f'File too large. Maximum size: {MAX_FILE_SIZE / 1024 / 1024:.1f}MB',
                    'code': 'FILE_TOO_LARGE'
                }
            )

        # 產生唯一檔案名稱
        file_extension = image.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # 儲存暫存檔案
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file_path = os.path.join(temp_dir, unique_filename)

            # 儲存檔案內容
            with open(temp_file_path, 'wb') as f:
                f.write(content)

            logger.info(f"收到檔案分析請求: {image.filename}")

            # 執行分析 - 如果客戶提供了 API key 則使用客戶的，否則使用系統預設
            if gemini_api_key:
                # 使用客戶提供的 API key
                result = analyzer.extract_id_info_with_key(temp_file_path, gemini_api_key)
            else:
                # 使用系統預設的 API key
                result = analyzer.extract_id_info(temp_file_path)

            # 加入請求 ID
            request_id = str(uuid.uuid4())
            result['request_id'] = request_id

            # 記錄分析結果
            logger.info(f"分析完成 (請求ID: {request_id}): {result.get('name', 'N/A')}")

            # 回傳結果
            if result.get('error'):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        'success': False,
                        'request_id': request_id,
                        'error': result['error'],
                        'code': 'ANALYSIS_FAILED'
                    }
                )
            else:
                # 增加使用次數
                new_usage_count = increment_usage_count()
                logger.info(f"使用次數更新: {new_usage_count}/{MAX_USAGE_COUNT}")

                # 使用格式化函數處理回應
                response = format_analysis_response(result, request_id)
                response['usage_info'] = {
                    'current_usage': new_usage_count,
                    'max_usage': MAX_USAGE_COUNT,
                    'remaining': MAX_USAGE_COUNT - new_usage_count
                }
                return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析請求處理失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

@app.post("/api/v1/analyze/passbook")
async def analyze_passbook(
    image: UploadFile = File(...),
    gemini_api_key: str = Form(None, description="Gemini API Key (如果不提供則使用系統預設)")
):
    """
    分析存摺圖片，提取帳戶名稱和存摺帳號

    Args:
        image: 存摺圖片檔案
        gemini_api_key: Gemini API Key (可選，如果不提供則使用系統預設)

    Returns:
        包含帳戶名稱和存摺帳號的分析結果
    """
    request_id = str(uuid.uuid4())
    logger.info(f"收到存摺分析請求: {request_id}")

    try:
        # 檢查使用次數限制
        if not check_usage_limit():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    'success': False,
                    'error': f'已達到使用上限 ({MAX_USAGE_COUNT} 次)，請聯繫管理員重置',
                    'code': 'USAGE_LIMIT_EXCEEDED',
                    'current_usage': get_usage_count(),
                    'max_usage': MAX_USAGE_COUNT
                }
            )

        # 檢查分析器是否可用
        if not passbook_analyzer:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={'success': False, 'error': 'Passbook analyzer not available', 'code': 'SERVICE_UNAVAILABLE'}
            )

        # 檢查檔案格式
        if not image.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={'success': False, 'error': 'No filename provided', 'code': 'INVALID_FILE'}
            )

        file_extension = image.filename.lower().split('.')[-1]
        if file_extension not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={'success': False, 'error': f'Unsupported file format: {file_extension}', 'code': 'INVALID_FORMAT'}
            )

        # 檢查檔案大小
        if image.size and image.size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail={'success': False, 'error': f'File too large: {image.size / 1024 / 1024:.2f}MB', 'code': 'FILE_TOO_LARGE'}
            )

        # 儲存暫存檔案
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_extension}') as temp_file:
            content = await image.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 執行存摺分析 - 如果客戶提供了 API key 則使用客戶的，否則使用系統預設
            if gemini_api_key:
                # 使用客戶提供的 API key
                result = passbook_analyzer.extract_passbook_info_with_key(temp_file_path, gemini_api_key)
            else:
                # 使用系統預設的 API key
                result = passbook_analyzer.extract_passbook_info(temp_file_path)

            if not result.get('success', False):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={'success': False, 'error': result.get('error', 'Analysis failed'), 'code': 'ANALYSIS_FAILED'}
                )

            # 準備回應資料
            data = {
                'request_id': request_id,
                'filename': image.filename,
                'file_size': len(content),
                'account_name': result.get('account_name'),
                'account_number': result.get('account_number'),
                'confidence': result.get('confidence', 0.0),
                'timestamp': result.get('timestamp'),
                'source': result.get('source', 'gemini')
            }

            # 添加郵局代號（如果有的話）
            if result.get('postal_code'):
                data['postal_code'] = result.get('postal_code')

            # 添加驗證警告或狀態資訊
            if result.get('account_number_warning'):
                data['account_number_warning'] = result.get('account_number_warning')
            if result.get('account_number_status'):
                data['account_number_status'] = result.get('account_number_status')

            # 增加使用次數
            new_usage_count = increment_usage_count()
            logger.info(f"存摺分析成功: {request_id}, 使用次數更新: {new_usage_count}/{MAX_USAGE_COUNT}")

            return {
                'success': True,
                'request_id': request_id,
                'data': data,
                'usage_info': {
                    'current_usage': new_usage_count,
                    'max_usage': MAX_USAGE_COUNT,
                    'remaining': MAX_USAGE_COUNT - new_usage_count
                }
            }

        finally:
            # 清理暫存檔案
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"清理暫存檔案失敗: {e}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"存摺分析失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={'success': False, 'error': 'Internal server error', 'code': 'INTERNAL_ERROR'}
        )

@app.get("/api/v1/config")
async def get_config():
    """取得系統設定資訊"""
    config_info = {
        'supported_formats': list(ALLOWED_EXTENSIONS),
        'supported_text_formats': list(ALLOWED_TEXT_EXTENSIONS),
        'max_file_size': f'{MAX_FILE_SIZE / 1024 / 1024:.0f}MB',
        'api_version': '2.0.0',
        'endpoints': {
            'analyze': '/api/v1/analyze',
            'analyze_passbook': '/api/v1/analyze/passbook',
            'analyze_text': '/api/v1/analyze-text',
            'xiabian_ju_chat': '/api/v1/xiabian-ju/chat',
            'xiabian_a_chat': '/api/v1/xiabian-a/chat',
            'config': '/api/v1/config',
            'health': '/health'
        },
        'available_apis': {
            'gemini': analyzer.gemini_model is not None if analyzer else False,
            'openai': analyzer.openai_api_key is not None if analyzer else False,
            'claude': analyzer.bedrock_client is not None if analyzer else False
        } if analyzer else {},
        'features': {
            'auto_side_detection': True,
            'multi_model_support': True,
            'form_submission': True,
            'comparison_analysis': True,
            'xiabian_chat': True
        },
        'xiabian_assistants': {
            'xiabian_ju': {
                'name': '小編JU',
                'description': '專精於美妝保養內容創作，語言風格輕鬆親切、活潑熱情',
                'endpoint': '/api/v1/xiabian-ju/chat'
            },
            'xiabian_a': {
                'name': '小編A',
                'description': '專精於內容創作，具有獨特的寫作風格和專業知識',
                'endpoint': '/api/v1/xiabian-a/chat'
            }
        } if assistants_manager else {},
        'usage_limits': {
            'max_file_size_mb': MAX_FILE_SIZE / 1024 / 1024,
            'max_usage_count': MAX_USAGE_COUNT,
            'current_usage': get_usage_count(),
            'remaining': MAX_USAGE_COUNT - get_usage_count(),
            'is_limit_reached': get_usage_count() >= MAX_USAGE_COUNT
        }
    }

    return {
        'success': True,
        'config': config_info
    }

@app.post("/api/v1/xiabian-ju/chat")
async def chat_with_xiabian_ju(request: XiabianChatRequest):
    """
    與小編JU對話

    小編JU專精於美妝保養內容創作，語言風格輕鬆親切、活潑熱情
    """
    try:
        # 檢查使用次數限制
        if not check_usage_limit():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    'success': False,
                    'error': f'已達到使用上限 ({MAX_USAGE_COUNT} 次)，請聯繫管理員重置',
                    'code': 'USAGE_LIMIT_EXCEEDED',
                    'current_usage': get_usage_count(),
                    'max_usage': MAX_USAGE_COUNT
                }
            )

        # 檢查服務是否已初始化
        if assistants_manager is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    'success': False,
                    'error': 'Assistants manager service is not available',
                    'code': 'SERVICE_UNAVAILABLE'
                }
            )

        request_id = str(uuid.uuid4())
        logger.info(f"收到小編JU對話請求 (請求ID: {request_id}): {request.message[:50]}...")

        # 與小編JU對話
        result = assistants_manager.chat_with_xiabian(
            xiabian_name="小編JU",
            user_message=request.message,
            thread_id=request.thread_id
        )

        if result.get('success'):
            # 增加使用次數
            new_usage_count = increment_usage_count()
            logger.info(f"小編JU對話成功 (請求ID: {request_id}), 使用次數更新: {new_usage_count}/{MAX_USAGE_COUNT}")

            return {
                'success': True,
                'request_id': request_id,
                'xiabian_name': '小編JU',
                'response': result['response'],
                'thread_id': result['thread_id'],
                'run_id': result['run_id'],
                'usage_info': {
                    'current_usage': new_usage_count,
                    'max_usage': MAX_USAGE_COUNT,
                    'remaining': MAX_USAGE_COUNT - new_usage_count
                }
            }
        else:
            logger.warning(f"小編JU對話失敗 (請求ID: {request_id}): {result.get('error')}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    'success': False,
                    'request_id': request_id,
                    'xiabian_name': '小編JU',
                    'error': result.get('error'),
                    'code': 'CHAT_FAILED'
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"小編JU對話請求處理失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

@app.post("/api/v1/xiabian-a/chat")
async def chat_with_xiabian_a(request: XiabianChatRequest):
    """
    與小編A對話

    小編A專精於內容創作，具有獨特的寫作風格和專業知識
    """
    try:
        # 檢查使用次數限制
        if not check_usage_limit():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    'success': False,
                    'error': f'已達到使用上限 ({MAX_USAGE_COUNT} 次)，請聯繫管理員重置',
                    'code': 'USAGE_LIMIT_EXCEEDED',
                    'current_usage': get_usage_count(),
                    'max_usage': MAX_USAGE_COUNT
                }
            )

        # 檢查服務是否已初始化
        if assistants_manager is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    'success': False,
                    'error': 'Assistants manager service is not available',
                    'code': 'SERVICE_UNAVAILABLE'
                }
            )

        request_id = str(uuid.uuid4())
        logger.info(f"收到小編A對話請求 (請求ID: {request_id}): {request.message[:50]}...")

        # 與小編A對話
        result = assistants_manager.chat_with_xiabian(
            xiabian_name="小編A",
            user_message=request.message,
            thread_id=request.thread_id
        )

        if result.get('success'):
            # 增加使用次數
            new_usage_count = increment_usage_count()
            logger.info(f"小編A對話成功 (請求ID: {request_id}), 使用次數更新: {new_usage_count}/{MAX_USAGE_COUNT}")

            return {
                'success': True,
                'request_id': request_id,
                'xiabian_name': '小編A',
                'response': result['response'],
                'thread_id': result['thread_id'],
                'run_id': result['run_id'],
                'usage_info': {
                    'current_usage': new_usage_count,
                    'max_usage': MAX_USAGE_COUNT,
                    'remaining': MAX_USAGE_COUNT - new_usage_count
                }
            }
        else:
            logger.warning(f"小編A對話失敗 (請求ID: {request_id}): {result.get('error')}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    'success': False,
                    'request_id': request_id,
                    'xiabian_name': '小編A',
                    'error': result.get('error'),
                    'code': 'CHAT_FAILED'
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"小編A對話請求處理失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

@app.post("/api/v1/analyze-text")
async def analyze_text_file(file: UploadFile = File(...)):
    """
    分析文字檔案（Markdown 或純文字）

    接受 Markdown 或純文字檔案，提取內容並準備給小編處理
    """
    try:
        # 檢查使用次數限制
        if not check_usage_limit():
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    'success': False,
                    'error': f'已達到使用上限 ({MAX_USAGE_COUNT} 次)，請聯繫管理員重置',
                    'code': 'USAGE_LIMIT_EXCEEDED',
                    'current_usage': get_usage_count(),
                    'max_usage': MAX_USAGE_COUNT
                }
            )

        # 檢查服務是否已初始化
        if assistants_manager is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    'success': False,
                    'error': 'Assistants manager service is not available',
                    'code': 'SERVICE_UNAVAILABLE'
                }
            )

        # 檢查檔案是否存在
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    'success': False,
                    'error': 'No file uploaded',
                    'code': 'NO_FILE'
                }
            )

        # 檢查檔案類型
        if not allowed_text_file(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    'success': False,
                    'error': f'File type not allowed. Supported types: {", ".join(ALLOWED_TEXT_EXTENSIONS)}',
                    'code': 'INVALID_FILE_TYPE'
                }
            )

        # 讀取檔案內容並檢查大小
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail={
                    'success': False,
                    'error': f'File too large. Maximum size: {MAX_FILE_SIZE / 1024 / 1024:.1f}MB',
                    'code': 'FILE_TOO_LARGE'
                }
            )

        # 根據檔案類型處理內容
        file_extension = file.filename.rsplit('.', 1)[1].lower()

        if file_extension in ['doc', 'docx']:
            # 處理 Word 文件
            try:
                # 儲存暫存檔案
                with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_extension}') as temp_file:
                    temp_file.write(content)
                    temp_file_path = temp_file.name

                try:
                    if file_extension == 'docx':
                        text_content = extract_text_from_docx(temp_file_path)
                    else:  # doc
                        text_content = extract_text_from_doc(temp_file_path)
                finally:
                    # 清理暫存檔案
                    try:
                        os.unlink(temp_file_path)
                    except:
                        pass

            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        'success': False,
                        'error': f'無法處理 Word 文件: {str(e)}',
                        'code': 'WORD_PROCESSING_ERROR'
                    }
                )
        else:
            # 處理純文字檔案
            try:
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    text_content = content.decode('big5')
                except UnicodeDecodeError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail={
                            'success': False,
                            'error': 'Unable to decode file. Please ensure it is a valid text file with UTF-8 or Big5 encoding.',
                            'code': 'ENCODING_ERROR'
                        }
                    )

        request_id = str(uuid.uuid4())
        logger.info(f"收到文字檔案分析請求 (請求ID: {request_id}): {file.filename}")

        # 分析檔案類型
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        if file_extension == "md":
            file_type = "markdown"
        elif file_extension in ["doc", "docx"]:
            file_type = "word"
        else:
            file_type = "text"

        # 增加使用次數
        new_usage_count = increment_usage_count()

        # 準備分析結果
        analysis_result = {
            'success': True,
            'request_id': request_id,
            'file_name': file.filename,
            'file_type': file_type,
            'content': text_content,
            'content_length': len(text_content),
            'word_count': len(text_content.split()),
            'line_count': len(text_content.splitlines()),
            'analysis_summary': f"已成功讀取 {file_type.upper()} 檔案，包含 {len(text_content.split())} 個字詞，{len(text_content.splitlines())} 行內容。",
            'usage_info': {
                'current_usage': new_usage_count,
                'max_usage': MAX_USAGE_COUNT,
                'remaining': MAX_USAGE_COUNT - new_usage_count
            }
        }

        logger.info(f"文字檔案分析完成 (請求ID: {request_id}): {file.filename}, 使用次數更新: {new_usage_count}/{MAX_USAGE_COUNT}")
        return analysis_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文字檔案分析請求失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

@app.post("/api/v1/admin/reset-usage")
async def reset_usage_count_api():
    """
    重置使用次數 (管理員功能)

    將使用次數重置為 0
    """
    try:
        success = reset_usage_count()
        if success:
            logger.info("使用次數已重置為 0")
            return {
                'success': True,
                'message': '使用次數已重置為 0',
                'current_usage': 0,
                'max_usage': MAX_USAGE_COUNT,
                'remaining': MAX_USAGE_COUNT
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    'success': False,
                    'error': '重置使用次數失敗',
                    'code': 'RESET_FAILED'
                }
            )
    except Exception as e:
        logger.error(f"重置使用次數失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

@app.get("/api/v1/usage-status")
async def get_usage_status():
    """
    取得目前使用狀態
    """
    try:
        current_usage = get_usage_count()
        return {
            'success': True,
            'current_usage': current_usage,
            'max_usage': MAX_USAGE_COUNT,
            'remaining': MAX_USAGE_COUNT - current_usage,
            'usage_percentage': (current_usage / MAX_USAGE_COUNT) * 100,
            'is_limit_reached': current_usage >= MAX_USAGE_COUNT
        }
    except Exception as e:
        logger.error(f"取得使用狀態失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'success': False,
                'error': 'Internal server error',
                'code': 'INTERNAL_ERROR'
            }
        )

def main():
    """主函數"""
    import argparse
    import uvicorn

    parser = argparse.ArgumentParser(description='智能身分證件資料處理 API 伺服器 v2.0')
    parser.add_argument('--host', default='127.0.0.1', help='伺服器主機 (預設: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=8008, help='伺服器埠號 (預設: 8008)')
    parser.add_argument('--reload', action='store_true', help='啟用自動重載')

    args = parser.parse_args()

    # 初始化服務
    if not init_services():
        print("❌ 服務初始化失敗，請檢查設定")
        print("💡 請確認以下設定：")
        print("   - .env 檔案中的 API 金鑰")
        print("   - 網路連線")
        print("   - 相關套件安裝")
        return

    print("🚀 智能身分證件資料處理 API 伺服器 v2.0")
    print("=" * 60)
    print(f"📍 伺服器地址: http://{args.host}:{args.port}")
    print(f"📚 API 文件: http://{args.host}:{args.port}/docs")
    print(f"❤️  健康檢查: http://{args.host}:{args.port}/health")
    print(f"⚙️  系統設定: http://{args.host}:{args.port}/api/v1/config")
    print()
    print("🔍 主要功能：")
    print("   • 智能面別檢測 (正面/背面)")
    print("   • 檔案分析 - 圖片格式 (/api/v1/analyze)")
    print("   • 正反面同時分析 (/api/v1/analyze/both)")
    print("   • 存摺分析 (/api/v1/analyze/passbook)")
    print("   • 檔案分析 - 文字格式 (/api/v1/analyze-text)")
    print("   • 多 AI 模型支援 (Gemini/OpenAI/Claude)")
    print("   • 動態欄位回傳")
    print("   • 表單自動提交")
    print("   • 小編JU對話 (/api/v1/xiabian-ju/chat)")
    print("   • 小編A對話 (/api/v1/xiabian-a/chat)")
    print("=" * 60)

    # 啟動伺服器
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.reload
    )

if __name__ == "__main__":
    main()
