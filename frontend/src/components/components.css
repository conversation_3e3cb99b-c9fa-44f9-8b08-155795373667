/* 檔案上傳元件樣式 */
.file-upload-container {
  width: 100%;
}

.dropzone {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  background: white;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.dropzone:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
}

.dropzone.active {
  border-color: var(--primary-500);
  background: var(--primary-100);
  transform: scale(1.02);
}

.dropzone.error {
  border-color: var(--error-500);
  background: #fef2f2;
}

.dropzone-content {
  position: relative;
  z-index: 2;
}

.upload-icon {
  color: var(--gray-400);
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: center;
}

.dropzone.active .upload-icon {
  color: var(--primary-500);
}

.dropzone h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-800);
}

.dropzone p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.size-limit {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 165, 233, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  border-radius: var(--radius-xl);
}

.drag-overlay p {
  font-weight: 600;
  color: var(--primary-600);
  font-size: 1.125rem;
}

.file-preview {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.file-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-100);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-600);
}

.file-details {
  flex: 1;
}

.file-details h4 {
  font-size: 1rem;
  margin-bottom: var(--spacing-xs);
  color: var(--gray-800);
}

.file-details p {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.remove-file {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gray-100);
  color: var(--gray-500);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all var(--transition-normal);
}

.remove-file:hover:not(:disabled) {
  background: var(--error-100);
  color: var(--error-600);
}

.upload-progress {
  margin-top: var(--spacing-md);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: 4px;
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.upload-success {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--success-500);
  font-weight: 500;
  margin-top: var(--spacing-md);
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--error-500);
  background: #fef2f2;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-md);
  font-size: 0.875rem;
}

/* 小編選擇器樣式 */
.xiabian-selector {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.selector-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-text h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xs);
}

.header-text p {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.xiabian-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.xiabian-option {
  position: relative;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 2px solid var(--gray-200);
  background: white;
  overflow: hidden;
}

.xiabian-option:hover:not(.disabled) {
  border-color: var(--gray-300);
  box-shadow: var(--shadow-md);
}

.xiabian-option.selected {
  border-color: transparent;
  box-shadow: var(--shadow-lg);
}

.xiabian-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.selection-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.option-content {
  position: relative;
  z-index: 2;
}

.option-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.option-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.option-info {
  flex: 1;
}

.option-info h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xs);
  transition: color var(--transition-normal);
}

.option-info p {
  font-size: 0.875rem;
  line-height: 1.5;
  transition: color var(--transition-normal);
}

.option-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.feature-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.selection-indicator {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  z-index: 3;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.selector-tip {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--gray-600);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  text-align: center;
  justify-content: center;
}

/* Prompt 自定義區域樣式 */
.prompt-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.prompt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.prompt-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: inherit;
}

.edit-prompt-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.edit-prompt-btn:hover:not(:disabled) {
  transform: scale(1.1);
  opacity: 0.8;
}

.edit-prompt-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prompt-editor {
  margin-top: var(--spacing-sm);
}

.prompt-textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.prompt-textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.prompt-textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.prompt-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.save-prompt-btn, .cancel-prompt-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.save-prompt-btn {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.save-prompt-btn:hover {
  background: rgba(34, 197, 94, 0.3);
  transform: translateY(-1px);
}

.cancel-prompt-btn {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.cancel-prompt-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.prompt-preview {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  line-height: 1.5;
  border: 1px solid rgba(0, 0, 0, 0.1);
  max-height: 100px;
  overflow-y: auto;
}

/* 內容展示元件樣式 */
.content-display {
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-2xl);
  text-align: center;
}

.loading-spinner {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-500);
}

.loading-state h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-800);
}

.loading-state p {
  color: var(--gray-600);
}

.error-state .error-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
}

.error-state h3 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
  color: var(--error-600);
}

.error-state p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
}

.retry-button {
  background: var(--error-500);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--transition-normal);
}

.retry-button:hover {
  background: var(--error-600);
  transform: translateY(-1px);
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-800);
}

.empty-state p {
  color: var(--gray-600);
  font-size: 1.125rem;
}

.blog-article {
  padding: var(--spacing-2xl);
  height: 100%;
  overflow-y: auto;
}

.article-header {
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
}

/* 文章互動按鈕 */
.article-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-100);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  background: var(--gray-50);
  color: var(--gray-600);
  border: 1px solid var(--gray-200);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.action-btn:hover {
  background: var(--gray-100);
  border-color: var(--gray-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.action-btn.liked {
  background: #fef2f2;
  color: #ef4444;
  border-color: #fecaca;
}

.action-btn.bookmarked {
  background: #eff6ff;
  color: #3b82f6;
  border-color: #bfdbfe;
}

.action-btn.copied {
  background: #f0fdf4;
  color: #22c55e;
  border-color: #bbf7d0;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.author-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.avatar-emoji {
  font-size: 1.5rem;
}

.author-details h4 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xs);
}

.author-details p {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.4;
}

.publish-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  align-items: flex-end;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--gray-500);
}

.article-content {
  line-height: 1.8;
  color: var(--gray-800);
}

.article-content h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
  color: var(--gray-900);
  line-height: 1.2;
}

.article-content h2 {
  font-size: 1.5rem;
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
  color: var(--gray-900);
  line-height: 1.3;
}

.article-content h3 {
  font-size: 1.25rem;
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  color: var(--gray-900);
  line-height: 1.4;
}

.article-content p {
  margin-bottom: var(--spacing-lg);
  font-size: 1rem;
  line-height: 1.8;
}

.article-content ul, .article-content ol {
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-xl);
}

.article-content li {
  margin-bottom: var(--spacing-sm);
  line-height: 1.7;
}

.article-content .quote {
  border-left: 4px solid var(--primary-500);
  padding-left: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  font-style: italic;
  color: var(--gray-700);
  background: var(--primary-50);
  padding: var(--spacing-lg);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.article-content .inline-code {
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: var(--gray-800);
}

.article-content .code-block {
  background: var(--gray-900);
  color: var(--gray-100);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin: var(--spacing-lg) 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
}

.article-footer {
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* 使用狀態組件樣式 */
.usage-status {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-lg);
}

.usage-status.loading {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-content: center;
  color: var(--gray-600);
  font-size: 0.875rem;
}

.usage-status.error {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-content: center;
  color: var(--error-500);
  font-size: 0.875rem;
}

.status-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: rgba(16, 185, 129, 0.1);
}

.status-text {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.status-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.status-count {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
}

.remaining-text {
  font-size: 0.75rem;
  color: var(--gray-600);
  text-align: right;
}

.limit-warning {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-md);
  color: var(--error-500);
  font-size: 0.75rem;
  font-weight: 500;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
