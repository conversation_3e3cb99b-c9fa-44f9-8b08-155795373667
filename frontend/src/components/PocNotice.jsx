import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Info, X, AlertCircle } from 'lucide-react';

const PocNotice = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="poc-notice"
        initial={{ opacity: 0, y: -50, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.95 }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 25,
          duration: 0.5 
        }}
      >
        <div className="notice-content">
          <div className="notice-icon">
            <AlertCircle size={20} />
          </div>
          <div className="notice-text">
            <h4>POC 版本提醒</h4>
            <p>
              此為概念驗證版本，AI 回應限制為 <strong>2048 tokens</strong> 以控制成本。
              正式版本將提供更長的回應內容。
            </p>
          </div>
          <motion.button
            className="notice-close"
            onClick={() => setIsVisible(false)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label="關閉提醒"
          >
            <X size={18} />
          </motion.button>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PocNotice;
