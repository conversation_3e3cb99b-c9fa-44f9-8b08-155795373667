import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Activity, AlertTriangle, RefreshCw } from 'lucide-react';
import { getUsageStatus } from '../services/api';

const UsageStatus = () => {
  const [usageData, setUsageData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchUsageStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getUsageStatus();
      if (response.success) {
        setUsageData(response);
      } else {
        setError('無法獲取使用狀態');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageStatus();
  }, []);

  if (loading) {
    return (
      <motion.div 
        className="usage-status loading"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <div className="loading-spinner">
          <RefreshCw className="animate-spin" size={16} />
        </div>
        <span>載入中...</span>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div 
        className="usage-status error"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <AlertTriangle size={16} />
        <span>無法載入使用狀態</span>
      </motion.div>
    );
  }

  if (!usageData) return null;

  const { current_usage, max_usage, remaining, usage_percentage, is_limit_reached } = usageData;
  
  const getStatusColor = () => {
    if (is_limit_reached) return '#ef4444'; // 紅色
    if (usage_percentage > 80) return '#f59e0b'; // 橙色
    return '#10b981'; // 綠色
  };

  const getStatusIcon = () => {
    if (is_limit_reached) return <AlertTriangle size={16} />;
    return <Activity size={16} />;
  };

  return (
    <motion.div 
      className="usage-status"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="status-header">
        <div className="status-icon" style={{ color: getStatusColor() }}>
          {getStatusIcon()}
        </div>
        <div className="status-text">
          <span className="status-label">使用狀態</span>
          <span className="status-count">
            {current_usage} / {max_usage}
          </span>
        </div>
      </div>

      <div className="progress-container">
        <div className="progress-bar">
          <motion.div 
            className="progress-fill"
            style={{ backgroundColor: getStatusColor() }}
            initial={{ width: 0 }}
            animate={{ width: `${usage_percentage}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
        </div>
        <span className="remaining-text">
          {is_limit_reached ? '已達上限' : `剩餘 ${remaining} 次`}
        </span>
      </div>

      {is_limit_reached && (
        <motion.div 
          className="limit-warning"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <AlertTriangle size={14} />
          <span>已達到使用上限，請聯繫管理員重置</span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default UsageStatus;
