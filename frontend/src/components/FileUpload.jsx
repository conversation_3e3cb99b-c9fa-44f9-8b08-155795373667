import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, File, X, CheckCircle, AlertCircle, Image, FileText } from 'lucide-react';
import { validateFile, formatFileSize, isTextFile } from '../services/api';

const FileUpload = ({ onFileUpload, isUploading, uploadProgress }) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [error, setError] = useState(null);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    setError(null);
    
    if (rejectedFiles.length > 0) {
      setError('請選擇有效的檔案 (圖片: PNG, JPG, JPEG, WEBP 或文字: Markdown, TXT, DOC, DOCX)');
      return;
    }

    const file = acceptedFiles[0];
    if (!file) return;

    try {
      validateFile(file);
      setUploadedFile(file);
      onFileUpload(file);
    } catch (err) {
      setError(err.message);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp'],
      'text/markdown': ['.md'],
      'text/plain': ['.txt'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles: 1,
    multiple: false
  });

  const removeFile = () => {
    setUploadedFile(null);
    setError(null);
  };

  return (
    <div className="file-upload-container">
      <AnimatePresence>
        {!uploadedFile && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className={`dropzone ${isDragActive ? 'active' : ''} ${error ? 'error' : ''}`}
            {...getRootProps()}
          >
            <input {...getInputProps()} />
            <div className="dropzone-content">
              <motion.div
                animate={{ 
                  scale: isDragActive ? 1.1 : 1,
                  rotate: isDragActive ? 5 : 0 
                }}
                transition={{ type: "spring", stiffness: 300 }}
                className="upload-icon"
              >
                <Upload size={48} />
              </motion.div>
              
              <h3>拖拽檔案到此處或點擊上傳</h3>
              <p>支援格式：PNG、JPG、JPEG、WEBP 檔案</p>
              <p>或 Markdown (.md)、純文字 (.txt)、Word (.doc/.docx) 檔案</p>
              <p className="size-limit">檔案大小限制：5MB</p>
              
              {isDragActive && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="drag-overlay"
                >
                  <p>放開以上傳檔案</p>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {uploadedFile && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="file-preview"
          >
            <div className="file-info">
              <div className="file-icon">
                {isTextFile(uploadedFile) ? <FileText size={24} /> : <Image size={24} />}
              </div>
              <div className="file-details">
                <h4>{uploadedFile.name}</h4>
                <p>{formatFileSize(uploadedFile.size)}</p>
              </div>
              <button 
                className="remove-file"
                onClick={removeFile}
                disabled={isUploading}
              >
                <X size={20} />
              </button>
            </div>

            {isUploading && (
              <div className="upload-progress">
                <div className="progress-bar">
                  <motion.div
                    className="progress-fill"
                    initial={{ width: 0 }}
                    animate={{ width: `${uploadProgress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <span className="progress-text">{Math.round(uploadProgress)}%</span>
              </div>
            )}

            {!isUploading && uploadProgress === 100 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="upload-success"
              >
                <CheckCircle size={20} />
                <span>上傳完成</span>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="error-message"
          >
            <AlertCircle size={20} />
            <span>{error}</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FileUpload;
