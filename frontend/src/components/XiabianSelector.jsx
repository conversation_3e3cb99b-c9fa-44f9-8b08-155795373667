import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, Pen, Palette } from 'lucide-react';

const XiabianSelector = ({ selectedXiabian, onXiabianChange, disabled = false }) => {
  const xiabianOptions = [
    {
      id: 'A',
      name: '小編A',
      description: '專精於內容創作，具有獨特的寫作風格和專業知識',
      icon: Pen,
      color: '#6366f1',
      gradient: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
    },
    {
      id: 'JU',
      name: '小編JU',
      description: '專精於美妝保養內容創作，語言風格輕鬆親切、活潑熱情',
      icon: Heart,
      color: '#ec4899',
      gradient: 'linear-gradient(135deg, #ec4899 0%, #f97316 100%)'
    }
  ];



  return (
    <div className="xiabian-selector">
      <div className="selector-header">
        <div className="header-icon">
          <Sparkles size={24} />
        </div>
        <div className="header-text">
          <h3>選擇 AI 小編</h3>
          <p>體驗不同的寫作風格</p>
        </div>
      </div>

      <div className="xiabian-options">
        {xiabianOptions.map((xiabian) => {
          const IconComponent = xiabian.icon;
          const isSelected = selectedXiabian === xiabian.id;
          
          return (
            <motion.div
              key={xiabian.id}
              className={`xiabian-option ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
              onClick={() => !disabled && onXiabianChange(xiabian.id)}
              whileHover={!disabled ? { scale: 1.02, y: -2 } : {}}
              whileTap={!disabled ? { scale: 0.98 } : {}}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              {/* 選中狀態的背景動畫 */}
              {isSelected && (
                <motion.div
                  className="selection-background"
                  layoutId="selectedBackground"
                  style={{ background: xiabian.gradient }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}

              <div className="option-content">
                <div className="option-header">
                  <div 
                    className="option-icon"
                    style={{ 
                      color: isSelected ? '#ffffff' : xiabian.color,
                      background: isSelected ? 'rgba(255,255,255,0.2)' : `${xiabian.color}15`
                    }}
                  >
                    <IconComponent size={24} />
                  </div>
                  <div className="option-info">
                    <h4 style={{ color: isSelected ? '#ffffff' : '#1f2937' }}>
                      {xiabian.name}
                    </h4>
                    <p style={{ color: isSelected ? 'rgba(255,255,255,0.8)' : '#6b7280' }}>
                      {xiabian.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* 選中指示器 */}
              <AnimatePresence>
                {isSelected && (
                  <motion.div
                    className="selection-indicator"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="indicator-dot" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </div>

      {/* 切換提示 */}
      <motion.div 
        className="selector-tip"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Palette size={16} />
        <span>
          {disabled 
            ? '上傳圖片後即可切換小編風格' 
            : '點擊切換不同的寫作風格，內容將重新生成'
          }
        </span>
      </motion.div>
    </div>
  );
};

export default XiabianSelector;
